'use client';

import React from 'react';
import { VedicChart as VedicChartData, ChartHouse } from '@/lib/astrology';
import { useUITranslation } from '@/utils/ui-translations';

interface VedicChartProps {
  chartData: VedicChartData;
  title: string;
  className?: string;
}

export default function VedicChart({ chartData, title, className = '' }: VedicChartProps) {
  const { t } = useUITranslation();

  // Generate unique IDs for SVG gradients to avoid conflicts when multiple charts are rendered
  const chartId = React.useMemo(() => Math.random().toString(36).substr(2, 9), []);

  // Debug logging
  console.log('🔍 VedicChart component received data:', {
    title,
    chartData,
    hasHouses: chartData?.houses?.length > 0,
    housesCount: chartData?.houses?.length || 0,
    firstHouse: chartData?.houses?.[0],
    ascendantHouse: chartData?.ascendantHouse,
    className,
    chartId,
    isDesktop: typeof window !== 'undefined' ? window.innerWidth >= 768 : 'unknown'
  });

  // Function to translate zodiac signs
  const translateSign = (sign: string): string => {
    const signMap: { [key: string]: string } = {
      'Aries': t('aries'),
      'Taurus': t('taurus'),
      'Gemini': t('gemini'),
      'Cancer': t('cancer'),
      'Leo': t('leo'),
      'Virgo': t('virgo'),
      'Libra': t('libra'),
      'Scorpio': t('scorpio'),
      'Sagittarius': t('sagittarius'),
      'Capricorn': t('capricorn'),
      'Aquarius': t('aquarius'),
      'Pisces': t('pisces')
    };
    return signMap[sign] || sign;
  };

  // Function to translate planet names
  const translatePlanet = (planet: string): string => {
    const planetMap: { [key: string]: string } = {
      'Sun': t('sun'),
      'Moon': t('moon'),
      'Mars': t('mars'),
      'Mercury': t('mercury'),
      'Jupiter': t('jupiter'),
      'Venus': t('venus'),
      'Saturn': t('saturn'),
      'Rahu': t('rahu'),
      'Ketu': t('ketu'),
      'Uranus': t('uranus'),
      'Neptune': t('neptune'),
      'Pluto': t('pluto')
    };
    return planetMap[planet] || planet;
  };

  // Traditional Vedic chart layout with clear sections and perfect spacing
  const renderTraditionalChart = () => {
    const houses = chartData?.houses || [];

    // Check if we have valid chart data
    if (!chartData || !houses || houses.length === 0) {
      console.log('🔍 VedicChart - No chart data available for:', title);
      console.log('🔍 Chart data:', chartData);
      console.log('🔍 Houses:', houses);
      return (
        <div className="relative w-full max-w-[500px] h-[500px] mx-auto flex items-center justify-center" style={{ display: 'block', visibility: 'visible' }}>
          <div className="text-center p-6 md:p-8 bg-purple-800/20 rounded-lg border border-purple-500/20">
            <h3 className="text-base md:text-lg font-semibold text-white mb-2">{t('chart_data_not_available')}</h3>
            <p className="text-purple-200 text-sm">
              {t('chart_being_calculated')}
            </p>
            <div className="text-xs text-purple-400 mt-2 bg-purple-900/30 rounded p-2">
              <p>Chart: {title}</p>
              <p>Houses: {houses?.length || 0}</p>
            </div>
          </div>
        </div>
      );
    }

    return (
      <div className="relative w-full max-w-[450px] md:max-w-[500px] lg:max-w-[550px] mx-auto z-10"
           style={{
             aspectRatio: '1/1',
             display: 'block !important',
             visibility: 'visible !important',
             opacity: 1
           }}>
        <svg viewBox="0 0 500 500"
             className="vedic-chart-svg w-full h-full drop-shadow-2xl"
             style={{
               display: 'block !important',
               maxWidth: '100%',
               height: 'auto',
               visibility: 'visible !important',
               opacity: 1
             }}>
          {/* Enhanced background with multiple gradients and effects */}
          <defs>
            <radialGradient id={`chartRadialGradient-${chartId}`} cx="50%" cy="50%" r="50%">
              <stop offset="0%" stopColor="#1e1b4b" stopOpacity="0.95"/>
              <stop offset="30%" stopColor="#312e81" stopOpacity="0.9"/>
              <stop offset="70%" stopColor="#4c1d95" stopOpacity="0.85"/>
              <stop offset="100%" stopColor="#0f0f23" stopOpacity="0.95"/>
            </radialGradient>
            <linearGradient id={`borderGradient-${chartId}`} x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="#fbbf24"/>
              <stop offset="25%" stopColor="#f97316"/>
              <stop offset="50%" stopColor="#ea580c"/>
              <stop offset="75%" stopColor="#f97316"/>
              <stop offset="100%" stopColor="#fbbf24"/>
            </linearGradient>
            <linearGradient id={`houseGradient-${chartId}`} x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="#1e1b4b" stopOpacity="0.8"/>
              <stop offset="100%" stopColor="#312e81" stopOpacity="0.6"/>
            </linearGradient>
            <filter id={`enhancedGlow-${chartId}`}>
              <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
              <feMerge>
                <feMergeNode in="coloredBlur"/>
                <feMergeNode in="SourceGraphic"/>
              </feMerge>
            </filter>
            <filter id={`textShadow-${chartId}`}>
              <feDropShadow dx="2" dy="2" stdDeviation="3" floodColor="#000000" floodOpacity="0.8"/>
            </filter>
          </defs>

          {/* Main chart background - traditional square with diamond inside */}
          <rect
            x="50" y="50" width="400" height="400"
            fill={`url(#chartRadialGradient-${chartId})`}
            stroke={`url(#borderGradient-${chartId})`}
            strokeWidth="5"
            rx="20"
            filter={`url(#enhancedGlow-${chartId})`}
            style={{ filter: 'drop-shadow(0 0 15px rgba(251, 191, 36, 0.4))' }}
          />

          {/* Traditional Vedic chart grid - 4x4 with clear sections */}
          {/* Vertical lines */}
          <line x1="150" y1="50" x2="150" y2="450" stroke={`url(#borderGradient-${chartId})`} strokeWidth="3" opacity="0.9" />
          <line x1="250" y1="50" x2="250" y2="450" stroke={`url(#borderGradient-${chartId})`} strokeWidth="3" opacity="0.9" />
          <line x1="350" y1="50" x2="350" y2="450" stroke={`url(#borderGradient-${chartId})`} strokeWidth="3" opacity="0.9" />

          {/* Horizontal lines */}
          <line x1="50" y1="150" x2="450" y2="150" stroke={`url(#borderGradient-${chartId})`} strokeWidth="3" opacity="0.9" />
          <line x1="50" y1="250" x2="450" y2="250" stroke={`url(#borderGradient-${chartId})`} strokeWidth="3" opacity="0.9" />
          <line x1="50" y1="350" x2="450" y2="350" stroke={`url(#borderGradient-${chartId})`} strokeWidth="3" opacity="0.9" />

          {/* Diagonal lines for traditional diamond pattern */}
          <line x1="150" y1="150" x2="350" y2="350" stroke={`url(#borderGradient-${chartId})`} strokeWidth="2" opacity="0.7" />
          <line x1="350" y1="150" x2="150" y2="350" stroke={`url(#borderGradient-${chartId})`} strokeWidth="2" opacity="0.7" />

          {/* House backgrounds for better visual separation */}
          {renderHouseBackgrounds()}

          {/* House numbers and content */}
          {renderTraditionalHouseContent(houses)}
        </svg>
      </div>
    );
  };

  // Render house backgrounds for better visual separation
  const renderHouseBackgrounds = () => {
    const houseRects = [
      { x: 50, y: 50, width: 100, height: 100, house: 12 },    // Top left
      { x: 150, y: 50, width: 100, height: 100, house: 1 },   // Top center-left
      { x: 250, y: 50, width: 100, height: 100, house: 2 },   // Top center-right
      { x: 350, y: 50, width: 100, height: 100, house: 3 },   // Top right
      { x: 350, y: 150, width: 100, height: 100, house: 4 },  // Right top
      { x: 350, y: 250, width: 100, height: 100, house: 5 },  // Right bottom
      { x: 250, y: 350, width: 100, height: 100, house: 6 },  // Bottom center-right
      { x: 150, y: 350, width: 100, height: 100, house: 7 },  // Bottom center-left
      { x: 50, y: 350, width: 100, height: 100, house: 8 },   // Bottom left
      { x: 50, y: 250, width: 100, height: 100, house: 9 },   // Left bottom
      { x: 50, y: 150, width: 100, height: 100, house: 10 },  // Left top
      { x: 350, y: 350, width: 100, height: 100, house: 11 }  // Bottom right
    ];

    return houseRects.map(rect => (
      <rect
        key={`bg-${rect.house}`}
        x={rect.x + 2}
        y={rect.y + 2}
        width={rect.width - 4}
        height={rect.height - 4}
        fill="url(#houseGradient)"
        opacity="0.3"
        rx="8"
      />
    ));
  };

  const renderTraditionalHouseContent = (houses: ChartHouse[]) => {
    // Debug logging
    console.log('🔍 VedicChart - Rendering houses:', houses);

    // Traditional Vedic chart house positions with perfect spacing and alignment
    const housePositions = [
      { x: 200, y: 100, house: 1, width: 100, height: 100 },   // Top center-left
      { x: 300, y: 100, house: 2, width: 100, height: 100 },   // Top center-right
      { x: 400, y: 100, house: 3, width: 100, height: 100 },   // Top right
      { x: 400, y: 200, house: 4, width: 100, height: 100 },   // Right top
      { x: 400, y: 300, house: 5, width: 100, height: 100 },   // Right bottom
      { x: 300, y: 400, house: 6, width: 100, height: 100 },   // Bottom center-right
      { x: 200, y: 400, house: 7, width: 100, height: 100 },   // Bottom center-left
      { x: 100, y: 400, house: 8, width: 100, height: 100 },   // Bottom left
      { x: 100, y: 300, house: 9, width: 100, height: 100 },   // Left bottom
      { x: 100, y: 200, house: 10, width: 100, height: 100 },  // Left top
      { x: 400, y: 400, house: 11, width: 100, height: 100 },  // Bottom right
      { x: 100, y: 100, house: 12, width: 100, height: 100 }   // Top left
    ];

    return housePositions.map((pos, posIndex) => {
      const house = houses.find(h => h.houseNumber === pos.house);
      if (!house) {
        console.log(`🔍 VedicChart - House ${pos.house} not found in data`);
        return null;
      }

      return (
        <g key={`house-${pos.house}-${posIndex}`}>
          {/* House number with enhanced styling and perfect positioning */}
          <text
            x={pos.x}
            y={pos.y - 30}
            textAnchor="middle"
            dominantBaseline="middle"
            className="text-lg font-bold fill-orange-300"
            filter={`url(#textShadow-${chartId})`}
            style={{
              fontSize: '18px',
              fontWeight: 'bold'
            }}
          >
            {pos.house}
          </text>

          {/* Sign name (translated) with perfect centering and larger text */}
          <text
            x={pos.x}
            y={pos.y - 8}
            textAnchor="middle"
            dominantBaseline="middle"
            className="text-sm fill-blue-200 font-semibold"
            filter={`url(#textShadow-${chartId})`}
            style={{
              fontSize: '14px',
              fontWeight: '600'
            }}
          >
            {translateSign(house.sign)}
          </text>

          {/* Planets in this house with better spacing and alignment */}
          {house.planets && house.planets.map((planet, index) => {
            // Calculate planet positions with better spacing
            const numPlanets = house.planets.length;
            let planetX, planetY;

            if (numPlanets === 1) {
              planetX = pos.x;
              planetY = pos.y + 20;
            } else if (numPlanets === 2) {
              planetX = pos.x + (index === 0 ? -20 : 20);
              planetY = pos.y + 20;
            } else if (numPlanets === 3) {
              if (index === 0) {
                planetX = pos.x;
                planetY = pos.y + 15;
              } else {
                planetX = pos.x + (index === 1 ? -20 : 20);
                planetY = pos.y + 30;
              }
            } else {
              planetX = pos.x + (index % 2 === 0 ? -20 : 20);
              planetY = pos.y + 15 + Math.floor(index / 2) * 15;
            }

            return (
              <g key={`house-${pos.house}-planet-${planet.name}-${index}`}>
                {/* Planet symbol with enhanced styling and perfect centering */}
                <text
                  x={planetX}
                  y={planetY}
                  textAnchor="middle"
                  dominantBaseline="middle"
                  className={`text-sm font-bold ${getPlanetColor(planet.name)}`}
                  filter={`url(#textShadow-${chartId})`}
                  style={{
                    fontSize: '16px',
                    fontWeight: 'bold'
                  }}
                >
                  {planet.symbol}
                  {planet.retrograde && (
                    <tspan className="text-red-300 text-sm font-bold">ᴿ</tspan>
                  )}
                </text>
              </g>
            );
          })}
        </g>
      );
    });
  };

  const getPlanetColor = (planetName: string): string => {
    const colors: { [key: string]: string } = {
      'Sun': 'fill-yellow-300',
      'Moon': 'fill-blue-200',
      'Mars': 'fill-red-300',
      'Mercury': 'fill-green-300',
      'Jupiter': 'fill-yellow-200',
      'Venus': 'fill-pink-300',
      'Saturn': 'fill-purple-300',
      'Rahu': 'fill-gray-300',
      'Ketu': 'fill-gray-200',
      'Uranus': 'fill-cyan-300',
      'Neptune': 'fill-blue-300',
      'Pluto': 'fill-indigo-300'
    };
    return colors[planetName] || 'fill-white';
  };

  return (
    <div className={`vedic-chart-container relative w-full bg-gradient-to-br from-purple-900/40 to-blue-900/40 backdrop-blur-md rounded-3xl p-4 md:p-6 lg:p-8 border border-purple-400/30 shadow-2xl overflow-visible ${className}`}
         style={{
           minHeight: '400px',
           display: 'block !important',
           visibility: 'visible !important',
           maxWidth: '100%',
           opacity: 1,
           position: 'relative',
           zIndex: 1
         }}>
      {/* Background decoration */}
      <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-blue-500/5 rounded-3xl"></div>
      <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_30%_20%,rgba(120,119,198,0.1),transparent_50%)]"></div>
      <div className="absolute bottom-0 right-0 w-full h-full bg-[radial-gradient(circle_at_70%_80%,rgba(59,130,246,0.1),transparent_50%)]"></div>

      <div className="relative z-10">
        <h3 className="text-xl md:text-2xl lg:text-3xl font-bold text-center text-white mb-4 md:mb-6 lg:mb-8 bg-gradient-to-r from-purple-200 via-blue-200 to-purple-200 bg-clip-text text-transparent drop-shadow-lg">
          ✨ {title} ✨
        </h3>
        {renderTraditionalChart()}

        {/* Enhanced Legend with beautiful styling */}
        <div className="mt-4 md:mt-6 lg:mt-8 space-y-3 md:space-y-4">
          <div className="bg-gradient-to-r from-black/30 to-black/20 backdrop-blur-md rounded-xl p-6 border border-white/20 shadow-lg">
            <div className="text-sm md:text-base text-gray-100 text-center space-y-3">
              <p className="font-semibold text-purple-100 flex items-center justify-center">
                <span className="mr-2">🏠</span>
                {t('houses_numbered_1_12')}
              </p>
              <p className="hidden md:block text-blue-100 flex items-center justify-center">
                <span className="mr-2">🌟</span>
                {t('planet_symbols_used')}
              </p>
              <p className="text-orange-100 flex items-center justify-center">
                <span className="mr-2">🔄</span>
                {t('r_indicates_retrograde')}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
